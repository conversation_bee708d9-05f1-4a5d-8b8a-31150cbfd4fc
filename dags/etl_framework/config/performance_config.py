"""
Performance Configuration - ETL Framework

Configurações centralizadas de performance para o framework ETL.
🚀 OTIMIZAÇÃO: Settings globais para controle fino de performance.
"""

from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, Optional


class PerformanceMode(Enum):
    """Modos de performance do framework"""
    DEVELOPMENT = "development"    # Full logging, validation habilitada
    PRODUCTION = "production"      # Minimal logging, validation otimizada  
    HIGH_PERFORMANCE = "high_performance"  # Logging mínimo, validation desabilitada


@dataclass
class PerformanceConfig:
    """
    Configuração global de performance do ETL Framework
    
    🚀 OTIMIZAÇÃO: Centraliza todas as configurações de performance
    """
    
    # 🚀 Modo de performance global
    mode: PerformanceMode = PerformanceMode.DEVELOPMENT
    
    # 🚀 Configurações de logging
    enable_verbose_logging: bool = True
    enable_chunk_logging: bool = True
    enable_progress_logging: bool = True
    log_cache_hits: bool = False
    
    # 🚀 Configurações de validação
    enable_validation: bool = True
    validation_cache_ttl: int = 300  # 5 minutos
    enable_deep_validation: bool = False
    max_validation_errors: int = 10
    
    # 🚀 Configurações de conexão
    default_pool_size: int = 8
    max_pool_overflow: int = 4
    connection_timeout: int = 300
    enable_connection_sharing: bool = True
    
    # 🚀 Configurações de processamento
    default_chunk_size: int = 75000
    max_parallel_tables: int = 4
    enable_lazy_loading: bool = True
    enable_strategy_pooling: bool = True
    
    # 🚀 Configurações de cache
    enable_query_caching: bool = True
    query_cache_ttl: int = 300
    enable_metadata_caching: bool = True
    metadata_cache_ttl: int = 900  # 15 minutos
    
    # 🚀 Configurações de timeout
    default_timeout_seconds: int = 300
    enable_timeout_warnings: bool = True
    timeout_retry_attempts: int = 2
    
    def is_production_mode(self) -> bool:
        """Verifica se está em modo production"""
        return self.mode in [PerformanceMode.PRODUCTION, PerformanceMode.HIGH_PERFORMANCE]
    
    def is_high_performance_mode(self) -> bool:
        """Verifica se está em modo high performance"""
        return self.mode == PerformanceMode.HIGH_PERFORMANCE
    
    def get_optimized_settings(self) -> Dict[str, Any]:
        """Retorna settings otimizados baseados no modo"""
        if self.is_high_performance_mode():
            return {
                'enable_validation': False,
                'enable_verbose_logging': False,
                'enable_chunk_logging': False,
                'enable_progress_logging': False,
                'default_chunk_size': 100000,  # Chunks maiores
                'max_parallel_tables': 6,      # Mais paralelismo
                'validation_cache_ttl': 0,     # No validation cache
                'enable_deep_validation': False
            }
        elif self.is_production_mode():
            return {
                'enable_validation': True,
                'enable_verbose_logging': False,
                'enable_chunk_logging': False,
                'enable_progress_logging': True,
                'default_chunk_size': 75000,
                'max_parallel_tables': 4,
                'validation_cache_ttl': 600,
                'enable_deep_validation': False
            }
        else:  # Development mode
            return {
                'enable_validation': True,
                'enable_verbose_logging': True,
                'enable_chunk_logging': True,
                'enable_progress_logging': True,
                'default_chunk_size': 50000,
                'max_parallel_tables': 3,
                'validation_cache_ttl': 300,
                'enable_deep_validation': True
            }


# 🚀 OTIMIZAÇÃO: Configurações predefinidas para diferentes cenários

def get_development_config() -> PerformanceConfig:
    """Configuração otimizada para desenvolvimento"""
    return PerformanceConfig(
        mode=PerformanceMode.DEVELOPMENT,
        enable_verbose_logging=True,
        enable_validation=True,
        default_chunk_size=50000,
        max_parallel_tables=3,
        enable_deep_validation=True
    )


def get_production_config() -> PerformanceConfig:
    """Configuração otimizada para produção"""
    return PerformanceConfig(
        mode=PerformanceMode.PRODUCTION,
        enable_verbose_logging=False,
        enable_chunk_logging=False,
        enable_validation=True,
        validation_cache_ttl=600,
        default_chunk_size=75000,
        max_parallel_tables=4,
        enable_deep_validation=False,
        default_pool_size=8,
        max_pool_overflow=4
    )


def get_high_performance_config() -> PerformanceConfig:
    """Configuração para máxima performance"""
    return PerformanceConfig(
        mode=PerformanceMode.HIGH_PERFORMANCE,
        enable_verbose_logging=False,
        enable_chunk_logging=False,
        enable_progress_logging=False,
        enable_validation=False,
        validation_cache_ttl=0,
        default_chunk_size=100000,
        max_parallel_tables=6,
        enable_deep_validation=False,
        default_pool_size=6,
        max_pool_overflow=2,
        enable_connection_sharing=True,
        enable_strategy_pooling=True
    )


# 🚀 OTIMIZAÇÃO: Instância global de configuração
_global_performance_config: Optional[PerformanceConfig] = None


def get_global_performance_config() -> PerformanceConfig:
    """Obtém configuração global de performance"""
    global _global_performance_config
    if _global_performance_config is None:
        # Por padrão, usa configuração de produção para otimização
        _global_performance_config = get_production_config()
    return _global_performance_config


def set_global_performance_config(config: PerformanceConfig):
    """Define configuração global de performance"""
    global _global_performance_config
    _global_performance_config = config


def configure_for_environment(environment: str = "production"):
    """Configura automaticamente baseado no ambiente"""
    if environment.lower() == "development":
        set_global_performance_config(get_development_config())
    elif environment.lower() == "production":
        set_global_performance_config(get_production_config())
    elif environment.lower() == "high_performance":
        set_global_performance_config(get_high_performance_config())
    else:
        raise ValueError(f"Ambiente não suportado: {environment}")


# 🚀 OTIMIZAÇÃO: Configuração automática baseada em variável de ambiente
import os

# Auto-configuração baseada em ENV
PERFORMANCE_ENV = os.getenv('ETL_PERFORMANCE_MODE', 'production').lower()
if PERFORMANCE_ENV in ['development', 'production', 'high_performance']:
    configure_for_environment(PERFORMANCE_ENV)