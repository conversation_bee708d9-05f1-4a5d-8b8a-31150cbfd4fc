#!/usr/bin/env python3
"""
Performance Optimization Test - ETL Framework

Script para testar as otimizações implementadas no framework.
🚀 VALIDAÇÃO: Verifica se todas as otimizações estão funcionando corretamente.
"""

import time
import logging
from typing import Dict, Any

# Configuração de logging para os testes
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_lazy_loading_strategies():
    """Testa lazy loading de estratégias"""
    logger.info("🧪 Testando Lazy Loading de Estratégias...")
    
    try:
        from etl_framework.strategies.smart_incremental import SmartIncrementalStrategy
        
        # Mock connections para teste
        class MockConnection:
            def __init__(self, name):
                self.name = name
        
        source_conn = MockConnection("source")
        target_conn = MockConnection("target")
        
        # Testa que estratégias não são instanciadas no __init__
        start_time = time.time()
        strategy = SmartIncrementalStrategy(
            source_connection=source_conn,
            target_connection=target_conn
        )
        init_time = time.time() - start_time
        
        # Verifica que atributos privados existem (lazy loading)
        assert hasattr(strategy, '_daily_strategy')
        assert hasattr(strategy, '_seven_days_strategy')
        assert hasattr(strategy, '_full_load_strategy')
        
        # Verifica que estratégias ainda são None
        assert strategy._daily_strategy is None
        assert strategy._seven_days_strategy is None
        assert strategy._full_load_strategy is None
        
        logger.info(f"✅ Lazy Loading: OK - Inicialização em {init_time:.3f}s (otimizada)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Lazy Loading: FALHOU - {str(e)}")
        return False


def test_conditional_logging():
    """Testa logging condicional"""
    logger.info("🧪 Testando Logging Condicional...")
    
    try:
        from etl_framework.utils.logger import ETLLogger, LoggingMode, setup_production_logger
        
        # Testa modo production (mínimo)
        prod_logger = setup_production_logger("test_prod")
        assert prod_logger.is_production is True
        
        # Testa modo development  
        dev_logger = ETLLogger("test_dev", mode=LoggingMode.DEVELOPMENT)
        assert dev_logger.is_production is False
        
        logger.info("✅ Logging Condicional: OK - Modos configurados corretamente")
        return True
        
    except Exception as e:
        logger.error(f"❌ Logging Condicional: FALHOU - {str(e)}")
        return False


def test_validation_caching():
    """Testa cache de validação"""
    logger.info("🧪 Testando Cache de Validação...")
    
    try:
        from etl_framework.processors.data_validator import DataValidator
        
        # Mock connections
        class MockConnection:
            def get_table_count(self, schema, table):
                return 1000
        
        source_conn = MockConnection()
        target_conn = MockConnection()
        
        # Testa validação desabilitada
        validator_disabled = DataValidator(
            source_conn, target_conn, enable_validation=False
        )
        
        # Mock table metadata
        class MockTableMetadata:
            name = "test_table"
            target_schema = "test_schema"
            table_prefix = "test_"
        
        table_meta = MockTableMetadata()
        
        # Deve retornar True sem executar validação
        is_valid, info = validator_disabled.validate_table(table_meta)
        assert is_valid is True
        assert info.get('validation_skipped') is True
        
        logger.info("✅ Cache de Validação: OK - Validação pode ser desabilitada")
        return True
        
    except Exception as e:
        logger.error(f"❌ Cache de Validação: FALHOU - {str(e)}")
        return False


def test_strategy_factory():
    """Testa strategy factory"""
    logger.info("🧪 Testando Strategy Factory...")
    
    try:
        from etl_framework.strategies.factory import get_strategy_factory, StrategyFactory
        from etl_framework.strategies.base import ETLMode
        
        # Testa singleton
        factory1 = get_strategy_factory()
        factory2 = get_strategy_factory()
        assert factory1 is factory2  # Deve ser mesma instância
        
        # Testa criação de factory local
        local_factory = StrategyFactory()
        stats = local_factory.get_pool_stats()
        assert 'pool_size' in stats
        assert stats['pool_size'] == 0  # Pool vazio inicialmente
        
        logger.info("✅ Strategy Factory: OK - Factory funcionando")
        return True
        
    except Exception as e:
        logger.error(f"❌ Strategy Factory: FALHOU - {str(e)}")
        return False


def test_performance_config():
    """Testa configurações de performance"""
    logger.info("🧪 Testando Configurações de Performance...")
    
    try:
        from etl_framework.config.performance_config import (
            get_global_performance_config,
            get_production_config,
            get_high_performance_config,
            PerformanceMode
        )
        
        # Testa configuração global
        global_config = get_global_performance_config()
        assert global_config is not None
        
        # Testa configuração de produção
        prod_config = get_production_config()
        assert prod_config.mode == PerformanceMode.PRODUCTION
        assert prod_config.is_production_mode() is True
        
        # Testa configuração high performance
        hp_config = get_high_performance_config()
        assert hp_config.mode == PerformanceMode.HIGH_PERFORMANCE
        assert hp_config.is_high_performance_mode() is True
        assert hp_config.enable_validation is False  # Validação desabilitada
        
        # Testa otimizações
        optimized = hp_config.get_optimized_settings()
        assert optimized['enable_validation'] is False
        assert optimized['default_chunk_size'] == 100000  # Chunks maiores
        
        logger.info("✅ Performance Config: OK - Configurações otimizadas")
        return True
        
    except Exception as e:
        logger.error(f"❌ Performance Config: FALHOU - {str(e)}")
        return False


def run_performance_tests() -> Dict[str, bool]:
    """Executa todos os testes de performance"""
    logger.info("🚀 INICIANDO TESTES DE OTIMIZAÇÃO DE PERFORMANCE")
    logger.info("=" * 60)
    
    tests = {
        "lazy_loading": test_lazy_loading_strategies,
        "conditional_logging": test_conditional_logging,
        "validation_caching": test_validation_caching,
        "strategy_factory": test_strategy_factory,
        "performance_config": test_performance_config
    }
    
    results = {}
    total_start = time.time()
    
    for test_name, test_func in tests.items():
        test_start = time.time()
        results[test_name] = test_func()
        test_time = time.time() - test_start
        logger.info(f"⏱️ {test_name}: {test_time:.3f}s")
        logger.info("-" * 40)
    
    total_time = time.time() - total_start
    
    # Sumário
    passed = sum(results.values())
    total = len(results)
    success_rate = (passed / total) * 100
    
    logger.info("📊 SUMÁRIO DOS TESTES:")
    logger.info(f"✅ Passaram: {passed}/{total} ({success_rate:.1f}%)")
    logger.info(f"⏱️ Tempo Total: {total_time:.3f}s")
    
    if success_rate == 100:
        logger.info("🎉 TODAS AS OTIMIZAÇÕES ESTÃO FUNCIONANDO!")
    else:
        logger.warning("⚠️ Algumas otimizações apresentaram problemas")
    
    return results


if __name__ == "__main__":
    results = run_performance_tests()
    
    # Exit code baseado nos resultados
    if all(results.values()):
        exit(0)  # Sucesso
    else:
        exit(1)  # Falha